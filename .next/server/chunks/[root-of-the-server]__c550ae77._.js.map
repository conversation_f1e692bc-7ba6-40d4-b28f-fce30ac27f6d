{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n// Check if Supabase is configured\nconst isSupabaseConfigured = supabaseUrl && supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_project_url' &&\n  supabaseAnonKey !== 'your_supabase_anon_key'\n\n// Client-side Supabase client\nexport const supabase = isSupabaseConfigured\n  ? createBrowserClient(supabaseUrl!, supabaseAnonKey!)\n  : null\n\n// Server-side Supabase client (for API routes)\nexport const createServerClient = () => {\n  if (!isSupabaseConfigured) {\n    throw new Error('Supabase is not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables.')\n  }\n  return createClient(supabaseUrl!, supabaseAnonKey!)\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  display_name: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Game {\n  id: string\n  white_player_id: string\n  black_player_id?: string\n  game_state: string // FEN notation\n  moves: string[] // Array of moves in algebraic notation\n  status: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  created_at: string\n  updated_at: string\n}\n\nexport interface GameMove {\n  id: string\n  game_id: string\n  player_id: string\n  move: string\n  fen_after: string\n  created_at: string\n}\n\nexport interface ChatMessage {\n  id: string\n  game_id: string\n  user_id: string\n  message: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAEN,kCAAkC;AAClC,MAAM,uBAAuB,eAAe,mBAC1C,gBAAgB,+BAChB,oBAAoB;AAGf,MAAM,WAAW,uCACpB,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAc;AAI/B,MAAM,qBAAqB;IAChC,uCAA2B;;IAE3B;IACA,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAc;AACpC", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/auth/callback/route.ts"], "sourcesContent": ["import { createServerClient } from '@/lib/supabase'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  const requestUrl = new URL(request.url)\n  const code = requestUrl.searchParams.get('code')\n  const error = requestUrl.searchParams.get('error')\n  const errorDescription = requestUrl.searchParams.get('error_description')\n\n  // Handle OAuth errors\n  if (error) {\n    console.error('OAuth error:', error, errorDescription)\n    return NextResponse.redirect(`${requestUrl.origin}/?error=${error}&error_description=${encodeURIComponent(errorDescription || '')}`)\n  }\n\n  if (code) {\n    try {\n      const supabase = createServerClient()\n\n      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)\n\n      if (exchangeError) {\n        console.error('Error exchanging code for session:', exchangeError)\n        return NextResponse.redirect(`${requestUrl.origin}/?error=auth_error&error_description=${encodeURIComponent(exchangeError.message)}`)\n      }\n\n      // Log successful authentication for debugging\n      if (data.user) {\n        console.log('User authenticated successfully:', {\n          id: data.user.id,\n          email: data.user.email,\n          metadata: data.user.user_metadata\n        })\n      }\n    } catch (error) {\n      console.error('Unexpected error in auth callback:', error)\n      return NextResponse.redirect(`${requestUrl.origin}/?error=unexpected_error&error_description=${encodeURIComponent('An unexpected error occurred during authentication')}`)\n    }\n  }\n\n  // Redirect to home page after successful authentication\n  return NextResponse.redirect(`${requestUrl.origin}/`)\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;IACtC,MAAM,OAAO,WAAW,YAAY,CAAC,GAAG,CAAC;IACzC,MAAM,QAAQ,WAAW,YAAY,CAAC,GAAG,CAAC;IAC1C,MAAM,mBAAmB,WAAW,YAAY,CAAC,GAAG,CAAC;IAErD,sBAAsB;IACtB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gBAAgB,OAAO;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,QAAQ,EAAE,MAAM,mBAAmB,EAAE,mBAAmB,oBAAoB,KAAK;IACrI;IAEA,IAAI,MAAM;QACR,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD;YAElC,MAAM,EAAE,IAAI,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC;YAElF,IAAI,eAAe;gBACjB,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,qCAAqC,EAAE,mBAAmB,cAAc,OAAO,GAAG;YACtI;YAEA,8CAA8C;YAC9C,IAAI,KAAK,IAAI,EAAE;gBACb,QAAQ,GAAG,CAAC,oCAAoC;oBAC9C,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;oBACtB,UAAU,KAAK,IAAI,CAAC,aAAa;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,2CAA2C,EAAE,mBAAmB,uDAAuD;QAC3K;IACF;IAEA,wDAAwD;IACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC;AACtD", "debugId": null}}]}